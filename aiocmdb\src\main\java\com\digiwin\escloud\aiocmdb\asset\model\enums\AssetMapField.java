package com.digiwin.escloud.aiocmdb.asset.model.enums;

public enum AssetMapField {
    ASSET_AI_ID("aiId"),
    ASSET_NAME("assetName"),
    EID("eid"),
    AIOPS_ITEM("aiopsItem"),
    AIOPS_ITEM_ID("aiopsItemId"),
    COLLECTED_TIME("collectedTime"),
    FLUME_TIMESTAMP("flumeTimestamp"),
    DEVICE_ID("deviceId"),
    OPERATION_STATUS("operationStatus"),
    USE_STATUS("useStatus"),
    AGENT_VERSION("agentVersion"),
    MANAGER_STATUS("managerStatus");

    private final String key;

    AssetMapField(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }
}
